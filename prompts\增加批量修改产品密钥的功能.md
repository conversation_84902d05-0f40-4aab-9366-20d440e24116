请用中文与我交流。

项目背景：
这是一个使用Python+Flask开发的充电桩管理系统的后端项目，数据库你已经帮我从SQLLite迁移到了PgSQL了。当前我正在查看设备管理页面相关的代码，包括：
- 前端模板：templates/devices.html（设备管理页面）
- 后端路由：routes/device.py、以及一些device相关的py文件。
- 相关的设备处理模块

当前存在的问题：
1. **缺少批量修改产品密钥product_key的功能**：
   - 现在修改产品密钥还是手动通过点击设备所在行右侧的编辑按钮进入设备编辑页面手动输入修改，这样很不方便，需要批量修改的功能。

2. **设备筛选的功能该不够完善**：需要新增更多的设备筛选功能，并且优化一下代码的实现。允许你对后端也进行优化修改。

3. **页面预览问题**：现在由于新增了很多的属性，导致想要点击编辑灯按钮需要鼠标选择文字滑动到最右侧很不方便。

解决方案要求：
需求：希望你能够完善的解决上面遇到的两个问题，在修改时注意相关的功能的实现也需要对应修改。
编辑按钮对应的元素在devices.html
<button class="btn btn-sm btn-warning" onclick="editDevice(1)" title="编辑"><i class="fas fa-edit"></i></button>



基于我们刚完成的充电桩设备管理页面功能改进，现在需要进一步优化用户界面和代码质量。请按以下优先级顺序完成任务：

## 主要任务

### 1. 优化筛选功能布局
- 将固件版本筛选从高级筛选区域移动到基础筛选区域，因为它是常用功能
- 重新排列基础筛选控件的顺序，确保最常用的筛选选项优先显示
- 保持筛选区域的整体布局美观和易用性

### 2. 解决批量操作按钮布局问题
- 分析当前批量操作按钮过多导致的布局问题（按钮换行、图标变大等）
- 设计更优雅的批量操作按钮布局方案，可考虑：
  - 使用下拉菜单或按钮组
  - 分类整理批量操作功能
  - 优化按钮间距和尺寸
- 确保在不同屏幕尺寸下都有良好的显示效果

### 3. 优化设备列表行显示
注：原指令第3点似乎未完整，请根据上下文推测可能的优化需求：
- 检查设备列表每行的信息密度和可读性
- 优化行高、间距和内容布局
- 确保重要信息突出显示

### 4. 修复暗黑模式兼容性问题
- 全面检查页面的暗黑模式实现
- 识别暗黑模式下显示不正确的UI元素
- 修复以下可能的问题：
  - 背景色和文字色对比度
  - 边框和阴影效果
  - 按钮和表单控件的样式
  - 模态框和下拉菜单的主题适配
- 确保暗黑模式下的用户体验与亮色模式一致

### 5. 代码提交
- 完成所有改进后，使用git进行本地代码提交
- 编写清晰的提交信息，描述本次改进的主要内容

## 实施要求
- 保持与现有代码风格的一致性
- 确保所有改动不影响现有功能
- 在移动设备和桌面设备上都要有良好的显示效果
- 提供适当的用户反馈和错误处理

请按照上述顺序逐步完成任务，并在每个主要步骤完成后进行说明。



请用中文与我交流。

项目背景：
这是一个使用Python+Flask开发的充电桩管理系统的后端项目，数据库已从SQLite迁移到PostgreSQL。当前我正在查看设备管理页面相关的代码，包括：
- 前端模板：templates/devices.html（设备管理页面）
- 后端路由：routes/device.py 以及其他设备相关的Python文件
- 相关的设备处理模块

当前需要完成的具体任务：

1. **优化设备列表操作按钮布局**：
   - 将每行设备后面的操作按钮改为平铺彩色显示，而不是下拉菜单
   - 增加整个设备卡片的宽度以容纳更多按钮
   - 对于超出宽度的操作按钮，实现水平滚动条功能
   - 确保按钮颜色区分明显，便于快速识别和点击

2. **重新组织批量操作功能**：
   - 将批量设置参数、批量调试脚本等按钮从下拉菜单中移出
   - 将这些批量功能按钮放置在页面顶部或侧边，方便调试使用
   - 确保批量操作按钮在视觉上与单个设备操作按钮有所区分

3. **增强分页导航功能**：
   - 在现有的"上一页"、"下一页"基础上，添加"回到首页"和"跳转到末页"功能
   - 可以考虑添加页码输入框，允许用户直接跳转到指定页面
   - 显示当前页码和总页数信息

4. **代码提交**：
   - 完成上述所有功能后，进行本地git提交
   - 提交信息应清晰描述所做的改进

实施要求：
- 在修改功能时，确保相关的前端模板（templates/devices.html）和后端路由（routes/device.py）都进行对应的更新
- 保持代码风格与现有项目一致，遵循项目的命名规范和代码结构
- 确保新功能与现有功能的兼容性，不破坏现有的设备管理功能
- 提供适当的用户反馈和错误处理，特别是在批量操作和分页跳转时
- 考虑响应式设计，确保在不同屏幕尺寸下都能正常显示和操作
- 在实现过程中，如果发现需要修改数据库查询或添加新的API端点，请一并处理

请按照任务顺序逐步实施，每完成一个任务后确认功能正常工作再进行下一个任务。