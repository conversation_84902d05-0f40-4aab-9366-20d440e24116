好的，给我一个脚本，现在数据库实际上还没有完全同步，后面我可能要自己同步一下。把这些数据库操作的脚本工具放到sql_toos文件夹下面方便管理，然后git本地提交项目。

下一步的任务：
下一步的任务和现在的工作紧密相关，就是这个项目之前全部使用的是SQLLite，现在你已经完成了数据库的迁移，那么现在需要你帮我把项目的数据库从sqllite更换为pgsql。项目使用的是python 数据库 SQLAlchemy引擎，使用了ORM技术。
任务细节：
代码改动尽可能少，注意数据表已经存在的问题，注意数据表的名称，确保可用性。
其次我之所以要更换pgsql就说ota任务的后台实现是多线程的，可能需要多线程读写，这个需要注意。
大原则是少改动除非为了完成任务必要才进行大量的修改。


请帮我完成以下两个任务：

**任务1：创建数据库管理工具脚本**
1. 创建一个名为 `sql_tools` 的文件夹
2. 将所有数据库迁移相关的脚本整理并移动到该文件夹中，包括：
   - 数据库连接测试脚本
   - 数据迁移脚本
   - 数据完整性验证脚本
   - 配置更新脚本
3. 在 `sql_tools` 文件夹中创建一个 README.md 文件，说明各个脚本的用途和使用方法
4. 完成后使用 git 将整个项目进行本地提交

**任务2：项目代码适配PostgreSQL数据库**
基于已完成的数据库迁移，现在需要修改项目代码以完全适配PostgreSQL：

**背景信息：**
- 项目原本使用SQLite数据库
- 已完成数据迁移到PostgreSQL（生产环境：kafangcharging/kafanglinlin_schema，调试环境：kfchargingdbg/kfchargingdbgc_schema）
- 项目使用Python + SQLAlchemy ORM
- OTA任务模块使用多线程，需要支持并发读写

**技术要求：**
1. **最小化代码改动原则**：只修改必要的部分，保持现有代码结构
2. **数据表兼容性**：确保代码能正确访问已迁移的PostgreSQL表（注意schema路径）
3. **多线程支持**：优化数据库连接配置以支持OTA任务的多线程并发访问
4. **向后兼容**：保留测试环境使用SQLite的能力（通过环境变量控制）

**具体修改点：**
1. 检查并更新所有直接的SQL查询，确保PostgreSQL兼容性
2. 优化SQLAlchemy连接池配置以支持多线程
3. 检查OTA任务相关的数据库操作，确保线程安全
4. 更新任何硬编码的数据库路径或表名引用
5. 测试关键功能确保迁移后正常工作

**验证要求：**
- 确保应用能在两种数据库环境下正常启动
- 验证OTA任务的多线程数据库操作正常
- 确认所有CRUD操作正常工作